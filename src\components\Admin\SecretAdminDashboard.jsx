import { useState, useEffect } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import AdminLogin from './AdminLogin';
import AdminLayout from './AdminLayout';
import AdminDashboard from './AdminDashboard';
import UserManagement from './UserManagement';
import AdminProfile from './AdminProfile';
import AdminRegistration from './AdminRegistration';
import adminService from '../../services/adminService';

const SecretAdminDashboard = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const location = useLocation();

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = async () => {
    setLoading(true);
    
    try {
      if (adminService.isAuthenticated()) {
        // Verify token is still valid by making a profile request
        await adminService.getProfile();
        setIsAuthenticated(true);
      } else {
        setIsAuthenticated(false);
      }
    } catch (error) {
      // Token is invalid or expired
      setIsAuthenticated(false);
      adminService.logout(); // Clear invalid session
    } finally {
      setLoading(false);
    }
  };

  const handleLoginSuccess = () => {
    setIsAuthenticated(true);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-red-500 mx-auto"></div>
          <p className="mt-4 text-white text-lg">Loading Admin Dashboard...</p>
        </div>
      </div>
    );
  }

  // If not authenticated and not on login page, redirect to login
  if (!isAuthenticated && !location.pathname.endsWith('/login')) {
    return <Navigate to="/secretdashboard/login" replace />;
  }

  // If authenticated and on login page, redirect to dashboard
  if (isAuthenticated && location.pathname.endsWith('/login')) {
    return <Navigate to="/secretdashboard" replace />;
  }

  return (
    <Routes>
      {/* Login route - accessible without authentication */}
      <Route 
        path="/login" 
        element={<AdminLogin onLoginSuccess={handleLoginSuccess} />} 
      />
      
      {/* Protected admin routes */}
      <Route path="/*" element={
        isAuthenticated ? (
          <AdminLayout>
            <Routes>
              <Route path="/" element={<AdminDashboard />} />
              <Route path="/users" element={<UserManagement />} />
              <Route path="/profile" element={<AdminProfile />} />
              <Route path="/register" element={<AdminRegistration />} />
              
              {/* Catch all route for admin area */}
              <Route path="*" element={<Navigate to="/secretdashboard" replace />} />
            </Routes>
          </AdminLayout>
        ) : (
          <Navigate to="/secretdashboard/login" replace />
        )
      } />
    </Routes>
  );
};

export default SecretAdminDashboard;
